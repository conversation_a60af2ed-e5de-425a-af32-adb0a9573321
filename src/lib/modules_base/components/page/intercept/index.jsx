import Taro, { useEffect, useRef } from '@tarojs/taro';
import isString from 'lodash/isString';
import isFunction from 'lodash/isFunction';
import { useSwitchShow } from './_utils';

/**
 *
 * @description 页面退出拦截
 * @param {{
 *    tips?: string|()=>Promise(boolean); // 退出时提示
 *    min?:number; // 二次退出，最小间隔时间；默认2000毫秒，设置-1（仅当tip为函数时，-1才有效），则是否可退出页面，依赖tip：()=>Promise(isBack)方法的配置；
 *    max?:number; // 最大时间，超过后退出再次提示； 默认1*60*1000毫秒；设置为-1，则不会一定时间后重新触发tip的逻辑；
 * }} props
 * @returns
 */
const PageIntercept = (props) => {
  const { show, run: setShow, ref: switchRef } = useSwitchShow(true);
  const { tips = '再按一次退出', min = 2000, max = 1 * 60 * 1000 } = props;
  const ref = useRef({ delayMaxTime: 0, start: 0, tipLock: false });

  // 终止
  const stop = () => clearTimeout(ref.current.delayMaxTime);

  // 退出
  const triggerExit = () => {
    ref.current.start = 0;
    setShow(false);
  };

  // 动态tip
  const triggerDynamicsTip = async () => {
    if (ref.current.tipLock) return;
    ref.current.tipLock = true;
    try {
      const isBack = await tips({ ref });
      if (isBack) {
        triggerExit();
      }
    } catch (error) {}
    ref.current.tipLock = false;
  };

  // 监听事件
  const handleEvent = async (e) => {
    const now = new Date().getTime();
    const { start } = ref.current;

    // 当前pageContainer是否显示
    const { show: curPageContainerShow } = switchRef.current;
    console.log(' -----', e);

    const _type = e.type.toLowerCase();
    switch (_type) {
      case 'beforeleave': // 离开前
        if (!curPageContainerShow) {
          // 已经设置为退出
          return;
        }
        setShow(true);

        if (!start) {
          ref.current.start = now;
        }

        const tipIsFunction = isFunction(tips); // 是否是函数
        console.log(tips);

        // 只有当tip为函数是 min=-1 才有效
        if (
          curPageContainerShow &&
          start > 0 &&
          (min >= 0 || !tipIsFunction) &&
          now - start >= min
        ) {
          triggerExit();
          return;
        }

        if (isString(tips)) {
          Taro.kbToast({
            text: tips,
          });
        } else if (tipIsFunction) {
          triggerDynamicsTip();
        }

        stop();
        if (max > 0) {
          ref.current.delayMaxTime = setTimeout(() => {
            ref.current.start = 0;
          }, max);
        }

        break;

      case 'afterleave': // 离开后
        if (!curPageContainerShow) {
          Taro.navigator();
        }
        break;

      default:
        break;
    }
  };

  useEffect(() => {
    return () => {
      stop();
    };
  }, []);

  console.log(props, '----')

  return (
    <page-container
      onBeforeEnter={handleEvent}
      onEnter={handleEvent}
      onAfterEnter={handleEvent}
      onBeforeLeave={handleEvent}
      onLeave={handleEvent}
      onAfterLeave={handleEvent}
      onClickOverlay={handleEvent}
      zIndex={-1}
      show={show}
      overlay={false}
      duration={0}
    />
  );
};

export default PageIntercept;
