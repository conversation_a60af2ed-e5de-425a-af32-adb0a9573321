/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getUserMobile, updateNickname } from '~/utils/qy';
import { checkIsFromShare } from '~/utils/share';
import KbActionSheet from '~base/components/action-sheet';
import KbLogin from '~base/components/login';
import KbAuthPhone from '~base/components/login/authPhone';
import KbModal from '~base/components/modal';
import KbSubscribeMsg from '~base/components/subscribe/subscribe-msg';
import KbToast from '~base/components/toast';
import GzhDrawer from '~/components/_pages/official-account/drawer';
import { extendMemo } from '~base/components/_utils';
import { page } from '~base/config';
import { usePostMessage, useUpdate } from '~base/hooks/page';
import { getPage } from '~base/utils/utils';
import { Form, Input, Text, View } from '@tarojs/components';
import Taro, { Fragment, useEffect, useState } from '@tarojs/taro';
import classNames from 'classnames';
import { AtButton } from 'taro-ui';
import { useTabBarShow } from '~/components/_pages/custom-tab-bar/_utils';
import KbPageNavBar from './nav-bar';
import PageIntercept from './intercept';
import './index.scss';

const { usePageEffect, useCheckAuthUserInfo, updateStorageTime } = page;

/**
 * 公共底部：
 * 承载页面需要引入的公共组件：登录组件、toast提示组件
 */
const ENV = Taro.getEnv();
const Index = (props) => {
  const {
    mastLogin,
    className,
    toast,
    modal,
    actionSheet,
    cover,
    shareOnce,
    navProps,
    closeIosSafeArea,
    intercept,
  } = props;
  const { $router: { params: routerParams } = {}, config: { navigationStyle } = {} } = getPage(-1);
  const isCustomNav = navigationStyle === 'custom';
  const show = useTabBarShow(props);
  const rootCls = classNames('kb-page', `kb-page-${ENV}`, className, {
    'kb-page-cover': cover,
    'kb-page-custom-bar': show, // 自定义tabBar
    'kb-page-custom-nav': isCustomNav, // 自定义导航
  });
  const [openAuthInfo, setOpenAuthInfo] = useState(false);
  const [nickname, setNickname] = useState();
  usePageEffect();
  const handleNickname = (ev) => {
    setNickname(ev.detail.value);
  };
  useCheckAuthUserInfo((isShowAuth) => {
    setOpenAuthInfo(isShowAuth);
  });
  useEffect(() => {
    if (!shareOnce) return;
    // 已分享的页面，屏蔽分享功能
    checkIsFromShare(routerParams) && Taro.hideShareMenu();
  }, [shareOnce, routerParams]);

  // sessionID有变化，才触发登录状态更新
  useUpdate((loginData, reLogin) => {
    const { userInfo: { openid } = {}, logined } = loginData;
    if (Taro.uma && openid) {
      // 友盟统计设置openID
      Taro.uma.setOpenid(openid);
    }
    if (Taro.wxAdSdk && openid) {
      // 微信微快递-SDK上报逻辑及服务协议修改(https://tower.im/teams/258300/todos/99459/)
      Taro.wxAdSdk.setOpenId(openid);
    }
    // 兼容到达页面后需要触发update的时机
    props.onUpdate(loginData, reLogin);
    if (!logined) return;
    //主动检测并同步其他端绑定手机号是否变更
    if (!Taro.hasCheckUserBindInfo) {
      Taro.hasCheckUserBindInfo = true;
      getUserMobile(true).then(({ mobile }) => {
        // 更新用户信息 - 手机号
        Taro.kbUpdateUserInfo({
          mobile,
        });
      }).catch(() => {
        Taro.hasCheckUserBindInfo = false;
      });
    }
  });

  console.log(intercept, '---intercept')

  return (
    <View className={rootCls}>
      <KbPageNavBar {...navProps}>
        <Fragment>{props.renderNavLeft}</Fragment>
      </KbPageNavBar>
      <View className='kb-page__header'>{props.renderHeader}</View>
      <View className='kb-page__body'>
        <View className='kb-page__body-view'>{props.children}</View>
      </View>
      <View className='kb-page__footer'>
        <View>{props.renderFooter}</View>
        {!closeIosSafeArea && (
          <View
            className={classNames('kb-page__footer-view', {
              'kb-page__footer-view--withoutFooter': !props.renderFooter,
            })}
          />
        )}
        <KbActionSheet {...actionSheet} />
        <KbModal {...modal} />
        <KbToast {...toast} />
        {/* <KbClipboard /> */}
        {/* 登录信息获取与更新 */}
        <KbLogin mastLogin={mastLogin} />
        {process.env.PLATFORM_ENV === 'weapp' &&
          (process.env.MODE_ENV === 'wkd' || process.env.MODE_ENV === 'yz') ? (
          <KbAuthPhone />
        ) : null}
        {process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV === 'weapp' ? (
          <GzhDrawer />
        ) : null}
      </View>
      <KbSubscribeMsg />
      {openAuthInfo ? (
        <KbModal
          isOpened={openAuthInfo}
          confirmText=''
          title='昵称信息'
          onCancel={() => {
            updateStorageTime();
            setOpenAuthInfo(false);
          }}
        >
          <View>
            <View>
              <Text>用户昵称</Text>
              <Text className='kb-size__sm kb-color__grey kb-margin-sm-l'>
                昵称信息仅用于个人身份信息标识
              </Text>
            </View>
            <View>
              <Form
                onSubmit={() => {
                  updateNickname(nickname).then(() => {
                    updateStorageTime();
                    setOpenAuthInfo(false);
                  });
                }}
              >
                <Input
                  name='nickname'
                  type='nickname'
                  placeholder='请输入您的昵称'
                  maxlength={10}
                  value={nickname}
                  onInput={handleNickname}
                  onBlur={handleNickname}
                  style={{
                    border: '1px solid #ccc',
                    margin: '20px 0',
                    padding: '10px 15px',
                    borderRadius: '3px',
                  }}
                />
                <AtButton type='primary' circle formType='submit'>
                  确定
                </AtButton>
              </Form>
            </View>
          </View>
        </KbModal>
      ) : null}
      {intercept && <PageIntercept {...intercept} />}
    </View>
  );
};

Index.defaultProps = {
  onUpdate: () => { },
  cover: true,
  shareOnce: false,
  navProps: null,
  mastLogin: true,
  allowAuthPopup: false,
};

Index.options = {
  addGlobalClass: true,
};

export { usePostMessage };

export default extendMemo(Index, ['toast', 'modal', 'actionSheet', 'navProps']);
